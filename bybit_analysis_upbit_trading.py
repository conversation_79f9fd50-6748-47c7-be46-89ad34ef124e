"""
Bybit 분석 + 업비트 매매 시스템
- 분석: Bybit 데이터 (BTCUSDT, ETHUSDT, SOLUSDT)
- 매매: 업비트 (KRW-BTC, KRW-ETH, KRW-SOL)
- 환율 무시하고 신호만 동기화
"""
import pandas as pd
import numpy as np
import pyupbit
import requests
import time
import os
from datetime import datetime
from dotenv import load_dotenv
import warnings
warnings.filterwarnings('ignore')

# 기존 전략 시스템 임포트
from trading_system import TradingSystem
from trading_system.core.base_strategy import SignalType

# 환경변수 로드
load_dotenv()


class BybitAnalysisUpbitTrading:
    """Bybit 분석 + 업비트 매매 시스템"""
    
    def __init__(self, live_trading: bool = False):
        """
        Args:
            live_trading: 실제 거래 여부 (False=시뮬레이션)
        """
        self.live_trading = live_trading
        
        # 심볼 매핑 (Bybit -> 업비트)
        self.symbol_mapping = {
            'BTCUSDT': 'KRW-BTC',
            'ETHUSDT': 'KRW-ETH', 
            'SOLUSDT': 'KRW-SOL'
        }
        
        # 업비트 API 초기화
        if live_trading:
            access_key = os.getenv("UPBIT_ACCESS_KEY")
            secret_key = os.getenv("UPBIT_SECRET_KEY")
            if not access_key or not secret_key:
                print("❌ 업비트 API 키가 설정되지 않았습니다!")
                print("💡 .env 파일에 API 키를 설정하거나 시뮬레이션 모드를 사용하세요.")
                raise ValueError("업비트 API 키가 설정되지 않았습니다!")
            self.upbit = pyupbit.Upbit(access_key, secret_key)
        else:
            self.upbit = None
            print("⚠️  시뮬레이션 모드 - 실제 거래하지 않습니다")
        
        # 트레이딩 시스템 초기화
        self.trading_system = TradingSystem("default")
        
        # 거래 설정
        self.max_investment_per_coin = 100000  # 종목당 최대 10만원
        self.order_amount = 20000  # 1회 주문 금액 2만원
        self.min_order_amount = 5000  # 최소 주문 금액
        
        # 상태 추적
        self.last_signals = {}
        
        print(f"🚀 Bybit 분석 + 업비트 매매 시스템 초기화")
        print(f"   - 실제 거래: {'ON' if live_trading else 'OFF'}")
        print(f"   - 대상 종목: {list(self.symbol_mapping.keys())}")
        print(f"   - 전략 수: {self.trading_system.get_system_status()['total_strategies']}개")
    
    def fetch_bybit_data(self, symbol: str, interval: str = "60", count: int = 200) -> pd.DataFrame:
        """Bybit에서 데이터 수집"""
        try:
            url = "https://api.bybit.com/v5/market/kline"
            
            # 시간 계산 (count개의 캔들)
            end_time = int(time.time() * 1000)
            start_time = end_time - (count * 60 * 60 * 1000)  # 60분봉 기준
            
            params = {
                "category": "linear",
                "symbol": symbol,
                "interval": interval,
                "start": start_time,
                "end": end_time,
                "limit": min(count, 1000)
            }
            
            response = requests.get(url, params=params)
            if response.status_code != 200:
                print(f"❌ {symbol} HTTP 오류: {response.status_code}")
                return pd.DataFrame()
            
            data = response.json()
            if data['retCode'] != 0:
                print(f"❌ {symbol} API 오류: {data['retMsg']}")
                return pd.DataFrame()
            
            # 데이터 처리
            klines = data['result']['list']
            if not klines:
                print(f"❌ {symbol} 데이터 없음")
                return pd.DataFrame()
            
            # DataFrame 생성
            df = pd.DataFrame(klines, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume', 'turnover'
            ])
            
            # 데이터 타입 변환
            df['timestamp'] = pd.to_datetime(df['timestamp'].astype('int64'), unit='ms')
            numeric_cols = ['open', 'high', 'low', 'close', 'volume']
            df[numeric_cols] = df[numeric_cols].astype(float)
            
            # 시간순 정렬 (오래된 것부터)
            df = df.sort_values('timestamp').reset_index(drop=True)
            df.set_index('timestamp', inplace=True)
            
            # 컬럼명 표준화
            df = df[['open', 'high', 'low', 'close', 'volume']]
            
            print(f"✅ {symbol} 데이터 수집 완료: {len(df)}개 캔들")
            return df
            
        except Exception as e:
            print(f"❌ {symbol} 데이터 수집 오류: {e}")
            return pd.DataFrame()
    
    def analyze_bybit_symbol(self, bybit_symbol: str) -> dict:
        """Bybit 심볼 분석"""
        # Bybit에서 데이터 수집
        df = self.fetch_bybit_data(bybit_symbol)
        if df.empty:
            return {'error': 'data_collection_failed'}
        
        try:
            # 전략 시스템으로 분석
            analysis = self.trading_system.analyze_symbol(df, bybit_symbol)
            
            # 현재 가격 정보 추가 (Bybit)
            current_price_bybit = df['close'].iloc[-1]
            analysis['bybit_price'] = current_price_bybit
            
            # 해당하는 업비트 가격도 가져오기
            upbit_symbol = self.symbol_mapping[bybit_symbol]
            upbit_price = pyupbit.get_current_price(upbit_symbol)
            analysis['upbit_price'] = upbit_price
            
            return analysis
            
        except Exception as e:
            print(f"❌ {bybit_symbol} 분석 오류: {e}")
            return {'error': str(e)}
    
    def get_upbit_account_info(self) -> dict:
        """업비트 계좌 정보 조회"""
        if not self.live_trading or not self.upbit:
            return {'krw_balance': 1000000, 'positions': {}}  # 시뮬레이션
        
        try:
            # KRW 잔고
            krw_balance = self.upbit.get_balance("KRW")
            
            # 보유 코인 잔고
            positions = {}
            for upbit_symbol in self.symbol_mapping.values():
                coin_name = upbit_symbol.split("-")[1]
                balance = self.upbit.get_balance(coin_name)
                if balance > 0:
                    current_price = pyupbit.get_current_price(upbit_symbol)
                    positions[upbit_symbol] = {
                        'balance': balance,
                        'current_price': current_price,
                        'value': balance * current_price
                    }
            
            return {
                'krw_balance': krw_balance,
                'positions': positions,
                'total_value': krw_balance + sum(pos['value'] for pos in positions.values())
            }
            
        except Exception as e:
            print(f"❌ 계좌 정보 조회 오류: {e}")
            return {'error': str(e)}
    
    def execute_upbit_trade(self, upbit_symbol: str, signal: str, confidence: float) -> bool:
        """업비트에서 거래 실행"""
        if not self.live_trading:
            print(f"📝 [시뮬레이션] {upbit_symbol}: {signal} (신뢰도: {confidence:.2f})")
            return True
        
        try:
            account = self.get_upbit_account_info()
            if 'error' in account:
                return False
            
            coin_name = upbit_symbol.split("-")[1]
            current_price = pyupbit.get_current_price(upbit_symbol)
            
            if signal in ['Buy', 'StrongBuy']:
                # 매수 로직
                available_krw = account['krw_balance']
                
                # 종목당 투자 한도 확인
                current_position_value = account['positions'].get(upbit_symbol, {}).get('value', 0)
                remaining_limit = self.max_investment_per_coin - current_position_value
                
                # 실제 주문 금액 계산
                order_amount = min(self.order_amount, available_krw, remaining_limit)
                
                if order_amount >= self.min_order_amount:
                    result = self.upbit.buy_market_order(upbit_symbol, order_amount)
                    if result:
                        print(f"✅ 매수 완료: {upbit_symbol} {order_amount:,}원")
                        return True
                    else:
                        print(f"❌ 매수 실패: {upbit_symbol}")
                        return False
                else:
                    print(f"⚠️  매수 스킵: {upbit_symbol} (주문금액 부족: {order_amount:,}원)")
                    return False
            
            elif signal in ['Sell', 'StrongSell']:
                # 매도 로직
                coin_balance = self.upbit.get_balance(coin_name)
                
                if coin_balance > 0:
                    result = self.upbit.sell_market_order(upbit_symbol, coin_balance)
                    if result:
                        print(f"✅ 매도 완료: {upbit_symbol} {coin_balance:.8f}개")
                        return True
                    else:
                        print(f"❌ 매도 실패: {upbit_symbol}")
                        return False
                else:
                    print(f"⚠️  매도 스킵: {upbit_symbol} (보유량 없음)")
                    return False
            
            return True
            
        except Exception as e:
            print(f"❌ {upbit_symbol} 거래 실행 오류: {e}")
            return False
    
    def run_analysis_cycle(self):
        """분석 사이클 실행"""
        print(f"\n{'='*70}")
        print(f"🔍 Bybit 분석 + 업비트 매매 사이클: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{'='*70}")
        
        # 업비트 계좌 정보 출력
        account = self.get_upbit_account_info()
        if 'error' not in account:
            print(f"💰 업비트 KRW 잔고: {account['krw_balance']:,.0f}원")
            if account['positions']:
                print("📊 업비트 보유 포지션:")
                for symbol, pos in account['positions'].items():
                    print(f"   {symbol}: {pos['balance']:.6f}개 ({pos['value']:,.0f}원)")
        
        # 각 심볼 분석 및 거래
        analysis_results = {}
        
        for bybit_symbol, upbit_symbol in self.symbol_mapping.items():
            print(f"\n📈 {bybit_symbol} (Bybit) 분석 → {upbit_symbol} (업비트) 거래")
            
            # Bybit 데이터로 분석
            analysis = self.analyze_bybit_symbol(bybit_symbol)
            if 'error' in analysis:
                print(f"❌ {bybit_symbol} 분석 실패: {analysis['error']}")
                continue
            
            analysis_results[bybit_symbol] = analysis
            
            # 결과 출력
            composite = analysis['composite_strategy']
            summary = analysis['summary']
            
            print(f"   Bybit 가격: ${analysis.get('bybit_price', 0):,.2f}")
            print(f"   업비트 가격: {analysis.get('upbit_price', 0):,.0f}원")
            print(f"   복합신호: {composite['signal']} (신뢰도: {composite['confidence']:.2f})")
            print(f"   개별신호: 매수 {summary['buy_signals']}개, "
                  f"매도 {summary['sell_signals']}개, 보류 {summary['hold_signals']}개")
            
            # 거래 실행 (업비트)
            signal = composite['signal']
            confidence = composite['confidence']
            
            # 신호 변화 확인 (같은 신호 반복 방지)
            last_signal = self.last_signals.get(bybit_symbol)
            if last_signal != signal:
                if signal in ['Buy', 'StrongBuy', 'Sell', 'StrongSell']:
                    success = self.execute_upbit_trade(upbit_symbol, signal, confidence)
                    if success:
                        self.last_signals[bybit_symbol] = signal
                else:
                    print(f"⏸️  {upbit_symbol}: 보류 ({signal})")
            else:
                print(f"🔄 {bybit_symbol}: 신호 변화 없음 ({signal})")
        
        return analysis_results
    
    def run_continuous(self, interval_minutes: int = 5):
        """연속 실행"""
        print(f"🔄 연속 실행 시작 (간격: {interval_minutes}분)")
        print("📊 Bybit 데이터 분석 → 업비트 거래 실행")
        print("Ctrl+C로 중지할 수 있습니다.")
        
        try:
            while True:
                self.run_analysis_cycle()
                
                print(f"\n⏰ {interval_minutes}분 대기 중...")
                time.sleep(interval_minutes * 60)
                
        except KeyboardInterrupt:
            print("\n🛑 사용자에 의해 중지되었습니다.")
        except Exception as e:
            print(f"\n❌ 시스템 오류: {e}")
    
    def run_single_analysis(self):
        """단일 분석 실행"""
        return self.run_analysis_cycle()
    
    def show_price_comparison(self):
        """가격 비교 표시"""
        print("\n💱 현재 가격 비교 (참고용)")
        print("-" * 50)
        
        for bybit_symbol, upbit_symbol in self.symbol_mapping.items():
            try:
                # Bybit 가격
                df = self.fetch_bybit_data(bybit_symbol, count=1)
                bybit_price = df['close'].iloc[-1] if not df.empty else 0
                
                # 업비트 가격
                upbit_price = pyupbit.get_current_price(upbit_symbol)
                
                print(f"{bybit_symbol:10} → {upbit_symbol:10}")
                print(f"  Bybit:  ${bybit_price:,.2f}")
                print(f"  업비트:  {upbit_price:,.0f}원")
                print()
                
            except Exception as e:
                print(f"❌ {bybit_symbol} 가격 조회 오류: {e}")


def main():
    """메인 실행 함수"""
    print("🚀 Bybit 분석 + 업비트 매매 시스템")
    print("=" * 50)
    print("📊 분석: Bybit (BTCUSDT, ETHUSDT, SOLUSDT)")
    print("💰 매매: 업비트 (KRW-BTC, KRW-ETH, KRW-SOL)")
    print("🔄 환율 무시, 신호만 동기화")
    
    # 실행 모드 선택
    print("\n실행 모드를 선택하세요:")
    print("1. 시뮬레이션 모드 (안전)")
    print("2. 실제 거래 모드 (주의!)")
    print("3. 가격 비교만 보기")
    
    try:
        choice = input("선택 (1, 2, 또는 3): ").strip()
        
        if choice == "3":
            # 가격 비교만
            system = BybitAnalysisUpbitTrading(live_trading=False)
            system.show_price_comparison()
            return
        
        elif choice == "1":
            live_trading = False
            print("✅ 시뮬레이션 모드 선택")
        elif choice == "2":
            live_trading = True
            confirm = input("⚠️  실제 거래 모드입니다. 정말 진행하시겠습니까? (yes/no): ").strip().lower()
            if confirm != "yes":
                print("❌ 취소되었습니다.")
                return
            print("⚠️  실제 거래 모드 선택")
        else:
            print("❌ 잘못된 선택입니다.")
            return
        
        # 시스템 초기화
        system = BybitAnalysisUpbitTrading(live_trading=live_trading)
        
        # 실행 방식 선택
        print("\n실행 방식을 선택하세요:")
        print("1. 단일 분석")
        print("2. 연속 실행 (5분 간격)")
        
        run_choice = input("선택 (1 또는 2): ").strip()
        
        if run_choice == "1":
            system.run_single_analysis()
        elif run_choice == "2":
            system.run_continuous(5)
        else:
            print("❌ 잘못된 선택입니다.")
            
    except Exception as e:
        print(f"❌ 시스템 오류: {e}")


if __name__ == "__main__":
    main()
